import 'dart:io';

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/model/media/media_model.dart';
import 'package:toii_social/model/post/hide_post_request.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/model/post/post_request.dart';
import 'package:toii_social/model/post/repost_request.dart';

part 'social_service.g.dart';

@RestApi()
abstract class SocialService {
  factory SocialService(Dio dio, {String baseUrl}) = _SocialService;

  @GET('/social/api/v1/feed')
  Future<BaseResponse<PostResponseDataModel>> getUserFeed();

  @POST('/social/api/v1/posts')
  Future<BaseResponse<PostModel>> createPost(
    @Body() CreatePostRequestModel post,
  );

  @PUT('/social/api/v1/posts/{id}')
  Future<void> updatePost(
    @Path("id") String id,
    @Body() PostRequest postRequest,
  );

  @POST('/social/api/v1/posts/repost')
  Future<void> repostPost(@Body() RepostRequest request);

  @GET('/social/api/v1/posts/{id}/comments')
  Future<BaseResponse<CommentListModel>> getCommentsOfPost(
    @Path("id") String id,
    @Query("include_replies") bool includeReplies,
  );

  @POST('/social/api/v1/posts/{id}/comments')
  Future<BaseResponse<CommentItemModel>> createCommentsOfPost(
    @Path("id") String id,
    @Body() CreateCommentRequestModel comment,
  );

  @MultiPart()
  @POST('/social/api/v1/media')
  Future<BaseResponse<MediaUploadResponseModel>> uploadMedia(
    @Part(name: 'file') File file,
    @Part(name: 'type') String type,
  );

  @POST('/social/api/v1/posts/{id}/interactions')
  Future<void> likePost(@Path('id') String id, @Query('type') String type);

  @DELETE('/social/api/v1/posts/{id}/interactions')
  Future<void> unlikePost(@Path('id') String id, @Query('type') String type);

  @DELETE('/social/api/v1/posts/{id}')
  Future<void> deletePost(@Path('id') String id);

  @GET('/social/api/v1/posts/{id}')
  Future<BaseResponse<PostModel>> getPostById(@Path('id') String id);

  @POST("/social/api/v1/comments/{commentId}/interactions")
  Future<void> likeComment(
    @Path('commentId') String commentId,
    @Query('type') String type,
  );

  @DELETE("/social/api/v1/comments/{commentId}/interactions")
  Future<void> unlikeComment(
    @Path('commentId') String commentId,
    @Query('type') String type,
  );
  @POST('/social/api/v1/posts/hide')
  Future<void> hidePost(@Body() HidePostRequest request);

  @GET('/social/api/v1/users/{userId}/posts')
  Future<BaseResponse<PostResponseDataModel>> getUserPosts(
    @Path('userId') String userId,
  );

  @GET('/social/api/v1/users/{userId}/reposts')
  Future<BaseResponse<PostResponseDataModel>> getUserReposts(
    @Path('userId') String userId,
  );
}
